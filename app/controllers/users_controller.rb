class UsersController < AdminController
  before_action :set_user, only: [ :show, :edit, :update, :destroy ]

  def index
    @users = User.order(:first_name, :last_name)
  end

  def show
    @dealerships = Dealership.active.order(:name)
  end

  def new
    @user = User.new
    @dealerships = Dealership.active.order(:name)
  end

  def edit
    @dealerships = Dealership.active.order(:name)
  end
  def create
    @user = User.new(user_params)

    # Set password - use provided password or generate temporary one
    if user_params[:password].present?
      @user.password_confirmation = @user.password
    else
      @user.password = generate_temp_password
      @user.password_confirmation = @user.password
      @user.password_change_required = true
    end

    if @user.save
      create_dealership_associations
      redirect_to user_path(@user), notice: "User created successfully."
    else
      @dealerships = Dealership.active.order(:name)
      render :new, status: :unprocessable_entity
    end
  end


  def update
    input_params = user_params
    if user_params[:password].blank?
      input_params = user_params.except(:password, :password_confirmation)
    end
    if @user.update(input_params)
      # update_dealership_associations
      redirect_to user_path(@user), notice: "User updated successfully."
    else
      @dealerships = Dealership.active.order(:name)
      render :edit, status: :unprocessable_entity
    end
  end

  def destroy
    @user.update!(
      status: :deleted,
    )
    redirect_to users_path, notice: "User deleted successfully."
  end

  private

  def set_user
    @user = User.find_by(uuid: params[:id])
  end

  def user_params
    params.expect(user: [
      :email, :first_name, :last_name, :phone, :user_type, :status,
      :password, :password_confirmation, :job_title, :preferred_language,
      :external_id, :time_zone, :password_change_required, :onboarding_completed,
      :photo,
      driver_license_attributes: [
        :id, :licence_number, :expiry_date, :issue_date, :category,
        :issuing_country, :issuing_state, :full_name, :date_of_birth,
        :verification_status, :verification_rejection_reason, :front_image,
        :back_image, :_destroy
      ]
    ])
  end

  def dealership_params
    params.expect(user: [ dealership_roles: {} ])
  end

  def generate_temp_password
    SecureRandom.alphanumeric(12) + "!"
  end

  def create_dealership_associations
    return if params[:user][:dealership_roles].blank?

    params[:user][:dealership_roles].each do |dealership_id, role|
      next if role.blank?

      @user.user_dealerships.create!(
        dealership_id: dealership_id,
        role: role
      )
    end
  end

  def update_dealership_associations
    @user.user_dealerships.destroy_all
    create_dealership_associations
  end
end
