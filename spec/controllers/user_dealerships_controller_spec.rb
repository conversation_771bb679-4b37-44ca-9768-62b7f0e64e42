require 'rails_helper'

RSpec.describe UserDealershipsController, type: :controller do
  let(:admin_user) { create(:user, :super_admin) }
  let(:user) { create(:user) }
  let(:dealership) { create(:dealership) }
  let(:user_dealership) { create(:user_dealership, user: user, dealership: dealership) }

  before do
    sign_in admin_user
  end

  describe 'POST #create' do
    let(:valid_params) do
      {
        user_id: user.uuid,
        user_dealership: {
          dealership_id: dealership.id,
          role: 'sales_person'
        }
      }
    end

    context 'with valid parameters' do
      it 'creates a new user_dealership' do
        expect {
          post :create, params: valid_params
        }.to change(UserDealership, :count).by(1)
      end

      it 'redirects to user show page with success notice' do
        post :create, params: valid_params
        expect(response).to redirect_to(user_path(user))
        expect(flash[:notice]).to eq('Dealership association created successfully.')
      end
    end

    context 'with invalid parameters' do
      let(:invalid_params) do
        {
          user_id: user.uuid,
          user_dealership: {
            dealership_id: nil,
            role: 'sales_person'
          }
        }
      end

      it 'does not create a user_dealership' do
        expect {
          post :create, params: invalid_params
        }.not_to change(UserDealership, :count)
      end

      it 'redirects with error message' do
        post :create, params: invalid_params
        expect(response).to redirect_to(user_path(user))
        expect(flash[:alert]).to include('Failed to create dealership association')
      end
    end
  end

  describe 'DELETE #destroy' do
    let!(:user_dealership) { create(:user_dealership, user: user, dealership: dealership) }

    it 'destroys the user_dealership' do
      expect {
        delete :destroy, params: { user_id: user.uuid, id: user_dealership.id }
      }.to change(UserDealership, :count).by(-1)
    end

    it 'redirects to user show page with success notice' do
      delete :destroy, params: { user_id: user.uuid, id: user_dealership.id }
      expect(response).to redirect_to(user_path(user))
      expect(flash[:notice]).to eq('Dealership association removed successfully.')
    end
  end
end
