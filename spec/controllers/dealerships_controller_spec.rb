require 'rails_helper'

RSpec.describe DealershipsController, type: :controller do
  let(:admin_user) { create(:user, :super_admin) }
  let!(:dealership1) { create(:dealership, name: "Toyota Downtown", status: :active) }
  let!(:dealership2) { create(:dealership, name: "Honda Uptown", status: :active) }
  let!(:dealership3) { create(:dealership, name: "Ford Central", status: :suspended) }

  before do
    sign_in admin_user
  end

  describe 'GET #autocomplete' do
    context 'without search query' do
      it 'returns all active dealerships' do
        get :autocomplete
        expect(response).to have_http_status(:ok)
        
        json_response = JSON.parse(response.body)
        expect(json_response.length).to eq(2) # Only active dealerships
        
        dealership_names = json_response.map { |d| d[1] }
        expect(dealership_names).to include("Toyota Downtown", "Honda Uptown")
        expect(dealership_names).not_to include("Ford Central") # suspended
      end

      it 'returns dealerships in correct format for ChoicesJS' do
        get :autocomplete
        json_response = JSON.parse(response.body)
        
        # Should be array of [id, name] arrays
        expect(json_response.first).to be_an(Array)
        expect(json_response.first.length).to eq(2)
        expect(json_response.first[0]).to be_an(Integer) # id
        expect(json_response.first[1]).to be_a(String)   # name
      end
    end

    context 'with search query' do
      it 'filters dealerships by name' do
        get :autocomplete, params: { q: 'Toyota' }
        expect(response).to have_http_status(:ok)
        
        json_response = JSON.parse(response.body)
        expect(json_response.length).to eq(1)
        expect(json_response.first[1]).to eq("Toyota Downtown")
      end

      it 'performs case-insensitive search' do
        get :autocomplete, params: { q: 'toyota' }
        expect(response).to have_http_status(:ok)
        
        json_response = JSON.parse(response.body)
        expect(json_response.length).to eq(1)
        expect(json_response.first[1]).to eq("Toyota Downtown")
      end

      it 'returns empty array when no matches found' do
        get :autocomplete, params: { q: 'Nonexistent' }
        expect(response).to have_http_status(:ok)
        
        json_response = JSON.parse(response.body)
        expect(json_response).to be_empty
      end
    end

    context 'with many dealerships' do
      before do
        # Create 25 dealerships to test the limit
        25.times do |i|
          create(:dealership, name: "Test Dealership #{i}", status: :active)
        end
      end

      it 'limits results to 20' do
        get :autocomplete
        expect(response).to have_http_status(:ok)
        
        json_response = JSON.parse(response.body)
        expect(json_response.length).to eq(20)
      end
    end
  end
end
